import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  Min,
  ValidateNested,
  IsEmail,
  IsPhoneNumber,
} from 'class-validator';
import { PaymentMethodEnum, PaymentStatusEnum, ShippingStatusEnum, OrderStatusEnum } from '../../enums';

/**
 * DTO cho thông tin sản phẩm trong đơn hàng
 */
export class OrderProductItemDto {
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID sản phẩm không được để trống' })
  @IsNumber({}, { message: 'ID sản phẩm phải là số' })
  @Min(1, { message: 'ID sản phẩm phải lớn hơn 0' })
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm',
    example: 2,
  })
  @IsNotEmpty({ message: 'S<PERSON> lượng không được để trống' })
  @IsNumber({}, { message: 'Số lượng phải là số' })
  @Min(1, { message: 'Số lượng phải lớn hơn 0' })
  quantity: number;
}

/**
 * DTO cho thông tin khách hàng trong đơn hàng
 */
export class OrderCustomerInfoDto {
  @ApiProperty({
    description: 'ID khách hàng (nếu có)',
    example: 101,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID khách hàng phải là số' })
  @Min(1, { message: 'ID khách hàng phải lớn hơn 0' })
  customerId?: number;

  @ApiProperty({
    description: 'Họ và tên khách hàng',
    example: 'Nguyễn Văn A',
  })
  @IsNotEmpty({ message: 'Họ và tên không được để trống' })
  @IsString({ message: 'Họ và tên phải là chuỗi' })
  @MaxLength(255, { message: 'Họ và tên không được vượt quá 255 ký tự' })
  fullName: string;

  @ApiProperty({
    description: 'Số điện thoại',
    example: '0912345678',
  })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @MaxLength(20, { message: 'Số điện thoại không được vượt quá 20 ký tự' })
  phone: string;

  @ApiProperty({
    description: 'Email khách hàng',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email không đúng định dạng' })
  @MaxLength(255, { message: 'Email không được vượt quá 255 ký tự' })
  email?: string;

  @ApiProperty({
    description: 'Địa chỉ khách hàng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
  })
  @IsNotEmpty({ message: 'Địa chỉ không được để trống' })
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  @MaxLength(500, { message: 'Địa chỉ không được vượt quá 500 ký tự' })
  address: string;
}

/**
 * DTO cho thông tin hóa đơn
 */
export class OrderBillInfoDto {
  @ApiProperty({
    description: 'Tổng tiền hàng',
    example: 300000,
  })
  @IsNotEmpty({ message: 'Tổng tiền hàng không được để trống' })
  @IsNumber({}, { message: 'Tổng tiền hàng phải là số' })
  @Min(0, { message: 'Tổng tiền hàng phải lớn hơn hoặc bằng 0' })
  subtotal: number;

  @ApiProperty({
    description: 'Thuế',
    example: 30000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Thuế phải là số' })
  @Min(0, { message: 'Thuế phải lớn hơn hoặc bằng 0' })
  tax?: number;

  @ApiProperty({
    description: 'Phí vận chuyển',
    example: 20000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Phí vận chuyển phải là số' })
  @Min(0, { message: 'Phí vận chuyển phải lớn hơn hoặc bằng 0' })
  shippingFee?: number;

  @ApiProperty({
    description: 'Giảm giá',
    example: 10000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Giảm giá phải là số' })
  @Min(0, { message: 'Giảm giá phải lớn hơn hoặc bằng 0' })
  discount?: number;

  @ApiProperty({
    description: 'Tổng tiền thanh toán',
    example: 340000,
  })
  @IsNotEmpty({ message: 'Tổng tiền thanh toán không được để trống' })
  @IsNumber({}, { message: 'Tổng tiền thanh toán phải là số' })
  @Min(0, { message: 'Tổng tiền thanh toán phải lớn hơn hoặc bằng 0' })
  total: number;

  @ApiProperty({
    description: 'Phương thức thanh toán',
    enum: PaymentMethodEnum,
    example: PaymentMethodEnum.COD,
  })
  @IsNotEmpty({ message: 'Phương thức thanh toán không được để trống' })
  @IsEnum(PaymentMethodEnum, { message: 'Phương thức thanh toán không hợp lệ' })
  paymentMethod: PaymentMethodEnum;

  @ApiProperty({
    description: 'Trạng thái thanh toán',
    enum: PaymentStatusEnum,
    example: PaymentStatusEnum.PENDING,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentStatusEnum, { message: 'Trạng thái thanh toán không hợp lệ' })
  paymentStatus?: PaymentStatusEnum;

  @ApiProperty({
    description: 'Số tiền thu hộ (COD)',
    example: 340000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số tiền thu hộ phải là số' })
  @Min(0, { message: 'Số tiền thu hộ phải lớn hơn hoặc bằng 0' })
  codAmount?: number;
}

/**
 * DTO cho thông tin vận chuyển
 */
export class OrderLogisticInfoDto {
  @ApiProperty({
    description: 'Phương thức vận chuyển',
    example: 'Giao hàng nhanh',
  })
  @IsNotEmpty({ message: 'Phương thức vận chuyển không được để trống' })
  @IsString({ message: 'Phương thức vận chuyển phải là chuỗi' })
  @MaxLength(255, { message: 'Phương thức vận chuyển không được vượt quá 255 ký tự' })
  shippingMethod: string;

  @ApiProperty({
    description: 'Đơn vị vận chuyển',
    example: 'GHN',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Đơn vị vận chuyển phải là chuỗi' })
  @MaxLength(100, { message: 'Đơn vị vận chuyển không được vượt quá 100 ký tự' })
  carrier?: string;

  @ApiProperty({
    description: 'Ghi chú vận chuyển',
    example: 'Giao hàng trong giờ hành chính',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Ghi chú vận chuyển phải là chuỗi' })
  @MaxLength(1000, { message: 'Ghi chú vận chuyển không được vượt quá 1000 ký tự' })
  shippingNote?: string;

  @ApiProperty({
    description: 'Địa chỉ giao hàng (nếu khác với địa chỉ khách hàng)',
    example: '456 Đường XYZ, Quận 2, TP.HCM',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ giao hàng phải là chuỗi' })
  @MaxLength(500, { message: 'Địa chỉ giao hàng không được vượt quá 500 ký tự' })
  deliveryAddress?: string;
}

/**
 * DTO chính cho việc tạo đơn hàng mới
 */
export class CreateUserOrderDto {
  @ApiProperty({
    description: 'Thông tin khách hàng',
    type: OrderCustomerInfoDto,
  })
  @IsNotEmpty({ message: 'Thông tin khách hàng không được để trống' })
  @ValidateNested()
  @Type(() => OrderCustomerInfoDto)
  customerInfo: OrderCustomerInfoDto;

  @ApiProperty({
    description: 'Danh sách sản phẩm',
    type: [OrderProductItemDto],
    example: [
      { productId: 1, quantity: 2 },
      { productId: 2, quantity: 1 },
    ],
  })
  @IsNotEmpty({ message: 'Danh sách sản phẩm không được để trống' })
  @IsArray({ message: 'Danh sách sản phẩm phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => OrderProductItemDto)
  products: OrderProductItemDto[];

  @ApiProperty({
    description: 'Thông tin hóa đơn',
    type: OrderBillInfoDto,
  })
  @IsNotEmpty({ message: 'Thông tin hóa đơn không được để trống' })
  @ValidateNested()
  @Type(() => OrderBillInfoDto)
  billInfo: OrderBillInfoDto;

  @ApiProperty({
    description: 'Đơn hàng có yêu cầu vận chuyển hay không',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường hasShipping phải là boolean' })
  hasShipping?: boolean;

  @ApiProperty({
    description: 'Thông tin vận chuyển',
    type: OrderLogisticInfoDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OrderLogisticInfoDto)
  logisticInfo?: OrderLogisticInfoDto;

  @ApiProperty({
    description: 'Trạng thái vận chuyển',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.PENDING,
    required: false,
  })
  @IsOptional()
  @IsEnum(ShippingStatusEnum, { message: 'Trạng thái vận chuyển không hợp lệ' })
  shippingStatus?: ShippingStatusEnum;

  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    enum: OrderStatusEnum,
    example: OrderStatusEnum.PENDING,
    required: false,
  })
  @IsOptional()
  @IsEnum(OrderStatusEnum, { message: 'Trạng thái đơn hàng không hợp lệ' })
  orderStatus?: OrderStatusEnum;

  @ApiProperty({
    description: 'Nguồn đơn hàng',
    example: 'website',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nguồn đơn hàng phải là chuỗi' })
  @MaxLength(45, { message: 'Nguồn đơn hàng không được vượt quá 45 ký tự' })
  source?: string;

  @ApiProperty({
    description: 'Ghi chú đơn hàng',
    example: 'Đơn hàng ưu tiên',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Ghi chú đơn hàng phải là chuỗi' })
  @MaxLength(1000, { message: 'Ghi chú đơn hàng không được vượt quá 1000 ký tự' })
  note?: string;

  @ApiProperty({
    description: 'Nhãn đơn hàng',
    example: ['urgent', 'vip'],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Nhãn đơn hàng phải là mảng' })
  @IsString({ each: true, message: 'Mỗi nhãn phải là chuỗi' })
  tags?: string[];
}
