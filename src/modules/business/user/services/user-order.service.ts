import { Injectable, Logger } from '@nestjs/common';
import { UserOrderRepository, UserProductRepository, UserConvertCustomerRepository } from '@modules/business/repositories';
import { PaginatedResult } from '@common/response';
import { QueryUserOrderDto, UserOrderListItemDto, UserOrderResponseDto, UserOrderStatusResponseDto, OrderStatusStatsDto, ShippingStatusStatsDto, UserConvertCustomerListItemDto, CreateUserOrderDto } from '../dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { OrderStatusEnum, ShippingStatusEnum, PaymentStatusEnum } from '../../enums';
import { UserOrder } from '../../entities';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý logic nghiệp vụ cho đơn hàng của người dùng
 */
@Injectable()
export class UserOrderService {
  private readonly logger = new Logger(UserOrderService.name);

  constructor(
    private readonly userOrderRepository: UserOrderRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
  ) {}

  /**
   * Tạo đơn hàng mới
   * @param userId ID người dùng
   * @param createOrderDto Dữ liệu tạo đơn hàng
   * @returns Thông tin đơn hàng đã tạo
   */
  @Transactional()
  async createOrder(userId: number, createOrderDto: CreateUserOrderDto): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Tạo đơn hàng mới cho userId=${userId}`);

      // 1. Validate và lấy thông tin sản phẩm
      const productInfoData = await this.validateAndGetProductInfos(userId, createOrderDto.products);

      // 2. Validate thông tin khách hàng
      const customerId = createOrderDto.customerInfo.customerId;
      const existingCustomer = await this.userConvertCustomerRepository.findById(customerId);

      if (!existingCustomer || existingCustomer.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOMER_NOT_FOUND,
          'Khách hàng không tồn tại hoặc không thuộc về bạn'
        );
      }

      // 3. Tạo đơn hàng
      const orderData: Partial<UserOrder> = {
        userId,
        userConvertCustomerId: customerId,
        productInfo: productInfoData,
        billInfo: {
          ...createOrderDto.billInfo,
          paymentStatus: createOrderDto.billInfo.paymentStatus || PaymentStatusEnum.PENDING,
        },
        hasShipping: createOrderDto.hasShipping ?? true,
        shippingStatus: createOrderDto.shippingStatus || ShippingStatusEnum.PENDING,
        logisticInfo: createOrderDto.logisticInfo ? {
          ...createOrderDto.logisticInfo,
          deliveryAddress: createOrderDto.logisticInfo.deliveryAddress || existingCustomer.address,
        } : {
          deliveryAddress: existingCustomer.address,
        },
        orderStatus: createOrderDto.orderStatus || OrderStatusEnum.PENDING,
        source: createOrderDto.source || 'website',
      };

      // Thêm thông tin bổ sung nếu có
      if (createOrderDto.note || createOrderDto.tags) {
        orderData.logisticInfo = {
          ...orderData.logisticInfo,
          note: createOrderDto.note,
          tags: createOrderDto.tags,
        };
      }

      const createdOrder = await this.userOrderRepository.createOrder(orderData);

      this.logger.log(`Đã tạo đơn hàng thành công với ID=${createdOrder.id}`);

      // 4. Trả về thông tin đơn hàng đã tạo
      return this.findById(createdOrder.id, userId);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Lỗi khi tạo đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Validate và lấy thông tin chi tiết sản phẩm
   * @param userId ID người dùng
   * @param products Danh sách sản phẩm trong đơn hàng
   * @returns Thông tin chi tiết sản phẩm
   */
  private async validateAndGetProductInfos(userId: number, products: any[]): Promise<Record<string, unknown>> {
    const productInfos: any[] = [];

    for (const productItem of products) {
      // Kiểm tra sản phẩm có tồn tại và thuộc về user không
      const product = await this.userProductRepository.findById(productItem.productId);
      if (!product || product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Sản phẩm với ID ${productItem.productId} không tồn tại hoặc không thuộc về bạn`
        );
      }

      // Tính toán giá và tổng tiền
      let unitPrice = 0;
      if (product.price && typeof product.price === 'object') {
        unitPrice = (product.price as any).salePrice || (product.price as any).listPrice || 0;
      }

      const totalPrice = unitPrice * productItem.quantity;

      productInfos.push({
        productId: product.id,
        name: product.name,
        quantity: productItem.quantity,
        unitPrice,
        totalPrice,
        description: product.description,
        images: product.images,
      });
    }

    return { products: productInfos };
  }



  /**
   * Lấy danh sách đơn hàng của người dùng với phân trang
   * @param userId ID người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách đơn hàng với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserOrderDto): Promise<PaginatedResult<UserOrderListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách đơn hàng cho userId=${userId}`);

      // Lấy danh sách đơn hàng từ repository
      const result = await this.userOrderRepository.findAll(userId, queryDto);

      // Chuyển đổi sang DTO response với thông tin customer đầy đủ
      const items = result.items.map(order => {
        const orderDto = plainToInstance(UserOrderListItemDto, order, { excludeExtraneousValues: true });

        // Map thông tin customer nếu có
        if (order.userConvertCustomer) {
          orderDto.userConvertCustomer = plainToInstance(
            UserConvertCustomerListItemDto,
            order.userConvertCustomer,
            { excludeExtraneousValues: true }
          );
        }

        return orderDto;
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách đơn hàng: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy danh sách đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID đơn hàng
   * @param userId ID người dùng
   * @returns Chi tiết đơn hàng
   */
  async findById(id: number, userId: number): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết đơn hàng id=${id} cho userId=${userId}`);

      // Lấy đơn hàng từ repository
      const order = await this.userOrderRepository.findById(id);

      // Kiểm tra đơn hàng tồn tại
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          `Không tìm thấy đơn hàng với ID ${id}`
        );
      }

      // Kiểm tra đơn hàng thuộc về người dùng
      if (order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
          `Bạn không có quyền truy cập đơn hàng này`
        );
      }

      // Chuyển đổi sang DTO response
      return plainToInstance(UserOrderResponseDto, order, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy chi tiết đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Lấy thống kê trạng thái đơn hàng và vận chuyển của người dùng
   * @param userId ID người dùng
   * @returns Thống kê trạng thái đơn hàng và vận chuyển
   */
  async getOrderStatusStats(userId: number): Promise<UserOrderStatusResponseDto> {
    try {
      this.logger.log(`Lấy thống kê trạng thái đơn hàng cho userId=${userId}`);

      // Lấy thống kê từ repository
      const stats = await this.userOrderRepository.getOrderStatusStats(userId);

      // Debug logs
      this.logger.log(`Raw stats from repository:`, {
        totalOrders: stats.totalOrders,
        ordersWithShipping: stats.ordersWithShipping,
        ordersWithoutShipping: stats.ordersWithoutShipping,
        orderStatus: stats.orderStatus,
        shippingStatus: stats.shippingStatus,
      });

      // Khởi tạo các giá trị mặc định cho order status (tất cả đơn hàng)
      const orderStatusStats: OrderStatusStatsDto = {
        pending: stats.orderStatus[OrderStatusEnum.PENDING] || 0,
        confirmed: stats.orderStatus[OrderStatusEnum.CONFIRMED] || 0,
        processing: stats.orderStatus[OrderStatusEnum.PROCESSING] || 0,
        completed: stats.orderStatus[OrderStatusEnum.COMPLETED] || 0,
        cancelled: stats.orderStatus[OrderStatusEnum.CANCELLED] || 0,
        total: stats.totalOrders, // Sử dụng tổng số đơn hàng thực tế
      };

      // Khởi tạo các giá trị mặc định cho shipping status (tất cả trạng thái từ enum đã làm sạch)
      const shippingStatusStats: ShippingStatusStatsDto = {
        pending: stats.shippingStatus[ShippingStatusEnum.PENDING] || 0,
        preparing: stats.shippingStatus[ShippingStatusEnum.PREPARING] || 0,
        shipped: stats.shippingStatus[ShippingStatusEnum.SHIPPED] || 0,
        inTransit: stats.shippingStatus[ShippingStatusEnum.IN_TRANSIT] || 0,
        sorting: stats.shippingStatus[ShippingStatusEnum.SORTING] || 0,
        delivered: stats.shippingStatus[ShippingStatusEnum.DELIVERED] || 0,
        deliveryFailed: stats.shippingStatus[ShippingStatusEnum.DELIVERY_FAILED] || 0,
        returning: stats.shippingStatus[ShippingStatusEnum.RETURNING] || 0,
        cancelled: stats.shippingStatus[ShippingStatusEnum.CANCELLED] || 0,
        total: stats.ordersWithShipping, // Chỉ đếm đơn hàng có vận chuyển
      };

      this.logger.log(`Final calculated stats for userId=${userId}:`, {
        orderStatus: orderStatusStats,
        shippingStatus: shippingStatusStats,
      });

      // Tạo response DTO
      const response: UserOrderStatusResponseDto = {
        orderStatus: orderStatusStats,
        shippingStatus: shippingStatusStats,
      };

      return plainToInstance(UserOrderStatusResponseDto, response, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`
      );
    }
  }
}
