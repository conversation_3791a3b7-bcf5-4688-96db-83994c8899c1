/**
 * Enum định nghĩa các phương thức thanh toán
 */
export enum PaymentMethodEnum {
  /**
   * Thanh toán khi nhận hàng (Cash on Delivery)
   */
  COD = 'COD',

  /**
   * Chuyển khoản ngân hàng
   */
  BANKING = 'BANKING',

  /**
   * Thẻ tín dụng
   */
  CREDIT_CARD = 'CREDIT_CARD',

  /**
   * Thẻ ghi nợ
   */
  DEBIT_CARD = 'DEBIT_CARD',

  /**
   * Ví điện tử
   */
  E_WALLET = 'E_WALLET',

  /**
   * QR Code
   */
  QR_CODE = 'QR_CODE',

  /**
   * Tiền mặt
   */
  CASH = 'CASH',

  /**
   * Khác
   */
  OTHER = 'OTHER'
}

/**
 * Mô tả phương thức thanh toán bằng tiếng Việt
 */
export const PAYMENT_METHOD_DESCRIPTIONS = {
  [PaymentMethodEnum.COD]: '<PERSON>h toán khi nhận hàng',
  [PaymentMethodEnum.BANKING]: 'Chuyển khoản ngân hàng',
  [PaymentMethodEnum.CREDIT_CARD]: 'Thẻ tín dụng',
  [PaymentMethodEnum.DEBIT_CARD]: 'Thẻ ghi nợ',
  [PaymentMethodEnum.E_WALLET]: 'Ví điện tử',
  [PaymentMethodEnum.QR_CODE]: 'QR Code',
  [PaymentMethodEnum.CASH]: 'Tiền mặt',
  [PaymentMethodEnum.OTHER]: 'Khác'
};
