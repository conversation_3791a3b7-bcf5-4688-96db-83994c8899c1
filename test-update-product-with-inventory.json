{"name": "Test Update Product with Inventory API", "description": "Test cases for PUT /v1/user/products/{id} with inventory support", "testCases": [{"name": "Update product with new inventory", "method": "PUT", "url": "/v1/user/products/1", "body": {"name": "<PERSON><PERSON> thun nam cao cấp - Updated", "price": {"listPrice": 250000, "salePrice": 200000, "currency": "VND"}, "description": "<PERSON><PERSON> thun nam chất liệu cotton cao cấp, form regular fit - Updated", "inventory": {"warehouseId": 1, "availableQuantity": 150, "sku": "SKU-UPDATE-001", "barcode": "1234567890123"}}, "expectedResponse": {"status": 200, "data": {"id": 1, "name": "<PERSON><PERSON> thun nam cao cấp - Updated", "price": {"listPrice": 250000, "salePrice": 200000, "currency": "VND"}, "inventory": {"warehouseId": 1, "availableQuantity": 150, "reservedQuantity": 0, "defectiveQuantity": 0, "currentQuantity": 150, "totalQuantity": 150, "sku": "SKU-UPDATE-001", "barcode": "1234567890123"}}}}, {"name": "Update product inventory only", "method": "PUT", "url": "/v1/user/products/1", "body": {"inventory": {"warehouseId": 2, "availableQuantity": 75, "sku": "SKU-UPDATE-002"}}, "expectedResponse": {"status": 200, "data": {"inventory": {"warehouseId": 2, "availableQuantity": 75, "reservedQuantity": 0, "defectiveQuantity": 0, "currentQuantity": 75, "totalQuantity": 75, "sku": "SKU-UPDATE-002"}}}}, {"name": "Update product with duplicate SKU (should fail)", "method": "PUT", "url": "/v1/user/products/2", "body": {"inventory": {"warehouseId": 1, "availableQuantity": 100, "sku": "SKU-UPDATE-001"}}, "expectedResponse": {"status": 400, "error": "Mã SKU \"SKU-UPDATE-001\" đã tồn tại trong sản phẩm khác của bạn"}}, {"name": "Update product with classifications and inventory", "method": "PUT", "url": "/v1/user/products/1", "body": {"name": "<PERSON><PERSON> thun nam đa dạng", "classifications": [{"type": "<PERSON><PERSON><PERSON>", "price": {"listPrice": 250000, "salePrice": 200000, "currency": "VND"}}], "inventory": {"warehouseId": 1, "availableQuantity": 200, "sku": "SKU-MULTI-001"}}, "expectedResponse": {"status": 200, "data": {"name": "<PERSON><PERSON> thun nam đa dạng", "classifications": [{"type": "<PERSON><PERSON><PERSON>", "price": {"listPrice": 250000, "salePrice": 200000, "currency": "VND"}}], "inventory": {"warehouseId": 1, "availableQuantity": 200, "reservedQuantity": 0, "defectiveQuantity": 0, "currentQuantity": 200, "totalQuantity": 200, "sku": "SKU-MULTI-001"}}}}], "notes": ["API PUT /v1/user/products/{id} now supports inventory updates", "Inventory field is optional in the request body", "SKU validation ensures uniqueness per user", "Only availableQuantity is required for inventory input", "reservedQuantity and defectiveQuantity are auto-calculated as 0", "currentQuantity and totalQuantity equal availableQuantity", "Inventory update uses the same validation as POST /v1/user/products/{id}/inventory"]}