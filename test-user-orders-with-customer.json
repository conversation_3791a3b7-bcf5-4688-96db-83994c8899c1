{"name": "Test GET /v1/user/orders with full customer information", "description": "Test cases for updated API that returns full userConvertCustomer instead of just userConvertCustomerId", "testCases": [{"name": "Get user orders with full customer information", "method": "GET", "url": "/v1/user/orders", "headers": {"Authorization": "Bearer {jwt_token}"}, "expectedResponse": {"status": 200, "data": {"items": [{"id": "4", "userConvertCustomer": {"id": 9, "name": "<PERSON><PERSON><PERSON><PERSON>", "email": ["<EMAIL>"], "phone": "********89", "avatar": "https://example.com/avatar.jpg", "platform": "Website", "timezone": "Asia/Ho_Chi_Minh", "metadata": {"source": "landing_page", "campaign": "summer_sale"}, "customFields": [{"configId": 1, "label": "<PERSON><PERSON><PERSON>", "type": "text", "value": "<PERSON><PERSON> sư ph<PERSON><PERSON> mềm"}], "tags": ["VIP", "<PERSON><PERSON><PERSON><PERSON> hàng thân thiết"], "createdAt": "*************", "updatedAt": "*************"}, "billInfo": {"total": 25000000, "status": "Paid", "payment_method": "Credit Card"}, "shippingStatus": "pending", "createdAt": "*************", "source": "Website", "orderStatus": "confirmed"}], "meta": {"totalItems": 1, "itemCount": 1, "itemsPerPage": 10, "totalPages": 1, "currentPage": 1}}}}, {"name": "Get user orders with null customer", "method": "GET", "url": "/v1/user/orders", "headers": {"Authorization": "Bearer {jwt_token}"}, "expectedResponse": {"status": 200, "data": {"items": [{"id": "5", "userConvertCustomer": null, "billInfo": {"total": ********, "status": "Pending", "payment_method": "Bank Transfer"}, "shippingStatus": "pending", "createdAt": "*************", "source": "Mobile App", "orderStatus": "pending"}]}}}, {"name": "Filter orders by customer ID", "method": "GET", "url": "/v1/user/orders?userConvertCustomerId=9", "headers": {"Authorization": "Bearer {jwt_token}"}, "expectedResponse": {"status": 200, "data": {"items": [{"id": "4", "userConvertCustomer": {"id": 9, "name": "<PERSON><PERSON><PERSON><PERSON>"}}]}}}], "changes": ["✅ Updated UserOrderListItemDto to include full userConvertCustomer object", "✅ Updated UserOrderRepository.findAll() to join with UserConvertCustomer", "✅ Updated UserOrderService.findAll() to map customer data properly", "✅ Updated Swagger documentation to reflect the change", "✅ Added UserConvertCustomerListItemDto import to service"], "beforeAfterComparison": {"before": {"userConvertCustomerId": "9"}, "after": {"userConvertCustomer": {"id": 9, "name": "<PERSON><PERSON><PERSON><PERSON>", "email": ["<EMAIL>"], "phone": "********89", "avatar": "https://example.com/avatar.jpg", "platform": "Website", "timezone": "Asia/Ho_Chi_Minh", "metadata": {}, "customFields": [], "tags": [], "createdAt": "*************", "updatedAt": "*************"}}}, "notes": ["API now returns full customer information instead of just ID", "Maintains backward compatibility for filtering by userConvertCustomerId", "Null customer handling is preserved", "Performance optimized with leftJoinAndSelect", "All existing query parameters still work"]}